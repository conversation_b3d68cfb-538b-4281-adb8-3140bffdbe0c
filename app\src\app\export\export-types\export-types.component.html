<cmdb-content-header
  title="Export Types">
</cmdb-content-header>

<form [formGroup]="formExport" (ngSubmit)="export()">
  <div class="card">
    <div class="card-body">
      <div class="form-group">
        <label for="typeChooser">Select Type <span class="required">*</span></label>
        <ng-select id="typeChooser"
                   [items]="typeList"
                   required
                   bindLabel="label"
                   bindValue="public_id"
                   placeholder="Choose your option"
                   formControlName="type">

          <ng-template ng-label-tmp let-item="item">
            <i class="{{item?.render_meta?.icon}}"></i> {{item?.label}}
            #{{item?.public_id}}
          </ng-template>
          <ng-template ng-option-tmp let-item="item" let-index="index">
            <i class="{{item?.render_meta?.icon}}"></i> {{item?.label}}
            #{{item?.public_id}}
          </ng-template>
        </ng-select>

        <!-- error block -->
        <div class="cross-validation-error-message alert alert-danger" *ngIf="isSubmitted && type.errors?.required">
          Please enter type
        </div>
      </div>

      <div class="form-group">
        <label for="formatChooser">Output format <span class="required">*</span></label>
        <ng-select id="formatChooser"
                   [items]="formatList"
                   required
                   bindLabel="label"
                   bindValue="id"
                   placeholder="Choose your option"
                   formControlName="format">

          <ng-template ng-label-tmp let-item="item">
            <fa-icon icon="{{item.icon}}"></fa-icon> {{item.label}}
          </ng-template>
          <ng-template ng-option-tmp let-item="item" let-index="index">
            <fa-icon icon="{{item.icon}}"></fa-icon> {{item.label}}
          </ng-template>
        </ng-select>
        <!-- error block -->
        <div class="cross-validation-error-message alert alert-danger" *ngIf="isSubmitted && format.errors?.required">
          Please enter format
        </div>
      </div>

      <button type="button" class="btn btn-primary float-right"
              (click)="this.export()"
              [disabled]="!formExport.valid">Go!</button>
    </div>
  </div>
</form>


<app-loading-popup 
  [isVisible]="isLoading$ | async"
  message="We're Processing the Data..."
></app-loading-popup>
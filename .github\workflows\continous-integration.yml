name: Continous Integration
on:
  push:
    branches:
      - '**'
    tags-ignore:
      - '**'

jobs:

  test:

    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: ['3.9' ]
        mongodb-version: ['4.4', '5.0', '6.0' ]

    steps:
      - uses: actions/checkout@v3
      - name: Start MongoDB
        uses: supercharge/mongodb-github-action@1.7.0
        with:
          mongodb-version: ${{ matrix.mongodb-version }}
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Display Python version
        run: python -c "import sys; print(sys.version)"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
          pip install pytest pytest-html coverage pytest-cov
      - name: Test with pytest
        run: pytest tests --html=pytest_report_py${{ matrix.python-version }}-modb-${{ matrix.mongodb-version }}.html --self-contained-html
      - name: Archive pytest reports
        uses: actions/upload-artifact@v4
        with:
          name: pytest-reports
          path: pytest_report_py${{ matrix.python-version }}-modb-${{ matrix.mongodb-version }}.html
        continue-on-error: true
      - name: Coverage pytest
        run: pytest tests --cov=cmdb --cov-config=.coveragerc --cov-report=html:cov-html_py${{ matrix.python-version }}-modb-${{ matrix.mongodb-version }}
      - name: Archive pytest coverage reports
        uses: actions/upload-artifact@v4
        with:
          name: pytest-coverate-reports
          path: cov-html_py${{ matrix.python-version }}-modb-${{ matrix.mongodb-version }}
        continue-on-error: true

  lint:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [ '3.9' ]
    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Display Python version
        run: python -c "import sys; print(sys.version)"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
          pip install pylint flake8
      - name: Lint with pylint
        run: pylint --rcfile=$CONFIG_FILE --fail-under=$FAIL_THRESHOLD ./cmdb | tee pylint-report_python-${{ matrix.python-version }}.txt
        env:
          FAIL_THRESHOLD: 5.0
          CONFIG_FILE: .pylintrc
      - name: Archive pylint results
        uses: actions/upload-artifact@v4
        with:
          name: pylint-reports
          path: pylint-report_python-${{ matrix.python-version }}.txt
        continue-on-error: true
      - name: Lint with flake8
        run: flake8 --exit-zero --config=$CONFIG_FILE --tee --output-file=flake8-report_python-${{ matrix.python-version }}.txt ./cmdb
        env:
          CONFIG_FILE: .flake8
      - name: Archive flake8 results
        uses: actions/upload-artifact@v4
        with:
          name: flake8-reports
          path: flake8-report_python-${{ matrix.python-version }}.txt
        continue-on-error: true

  coverage:
    needs: [ test ]
    runs-on: ubuntu-latest
    strategy:
      matrix:
        python-version: [ '3.9' ]

    steps:
      - uses: actions/checkout@v3
      - name: Set up Python ${{ matrix.python-version }}
        uses: actions/setup-python@v4
        with:
          python-version: ${{ matrix.python-version }}
      - name: Display Python version
        run: python -c "import sys; print(sys.version)"
      - name: Install dependencies
        run: |
          python -m pip install --upgrade pip
          pip install coverage
          if [ -f requirements.txt ]; then pip install -r requirements.txt; fi
      - name: Coverage source
        run: |
          coverage run -m cmdb
          coverage report -m
          coverage html -d coverage-report_python-${{ matrix.python-version }}
      - name: Archive coverage results
        uses: actions/upload-artifact@v4
        with:
          name: coverage-reports
          path: coverage-report_python-${{ matrix.python-version }}
        continue-on-error: true
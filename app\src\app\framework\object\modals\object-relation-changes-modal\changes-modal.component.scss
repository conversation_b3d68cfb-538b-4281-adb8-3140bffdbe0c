.modal-backdrop {
    position: fixed;
    top: 0;
    left: 0;
    z-index: 1040;
    width: 100vw;
    height: 100vh;
    background-color: rgba(0, 0, 0, 0.4);
  }
  
  .modal {
    position: fixed;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    z-index: 1050;
    width: 100%;
    max-width: 600px;
    margin: 0 auto;
  }
  
  .modal-content {
    border-radius: 4px;
    border: 1px solid #ddd;
  }
  


.modal-header {
    background: #e94d18;
    padding: 12px 20px;
    border-bottom: none;
    
    .modal-title {
      color: white;
      font-size: 18px;
      font-weight: 600;
    }
    
    .close {
      color: white;
      text-shadow: none;
      opacity: 0.9;
      
      &:hover {
        color: white;
        opacity: 1;
      }
      
      span {
        font-size: 28px;
        line-height: 0.8;
      }
    }
  }

  .modal-body {
    padding: 15px;
    max-height: 70vh;
    overflow-y: auto;
  }
  
  .detail-item {
    margin-bottom: 8px;
    label {
      color: #666;
      font-size: 13px;
      font-weight: 500;
    }
    div {
      color: #444;
      font-size: 14px;
    }
  }
  
  .changes-section {
    margin-top: 15px;
    .section-title {
      color: #e94d18;
      font-size: 15px;
      font-weight: 600;
      border-bottom: 2px solid #e94d18;
      padding-bottom: 6px;
      margin-bottom: 12px;
    }
  }
  
  .change-item {
    &.create-item {
      padding: 10px 15px;
      margin-bottom: 8px;
      background: #f8f8f8;
      border-radius: 4px;
      
      .field-line {
        display: grid;
        grid-template-columns: minmax(120px, max-content) 1fr;
        gap: 15px;
        align-items: baseline;
        
        @media (max-width: 480px) {
          grid-template-columns: 1fr;
          gap: 4px;
        }
      }
  
      .field-name {
        color: #e94d18;
        font-weight: 500;
        word-break: break-word;
        hyphens: auto;
        padding-right: 8px;
        position: relative;
        
        &::after {
          content: ':';
          position: absolute;
          right: 0;
        }
      }
  
      .change-value {
        color: #444;
        font-size: 14px;
        word-break: break-word;
        overflow-wrap: anywhere;
        line-height: 1.4;
        display: flex;
        align-items: center;
        min-height: 24px; // Maintain vertical alignment
      }
    }
  }
  
  .change-details {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 10px;
    margin-top: 8px;
    .badge {
      font-size: 12px;
      padding: 4px 8px;
      border-radius: 3px;
      &.before {
        background: #cccccc;
        color: #333;
      }
      &.after {
        background: #e94d18;
        color: white;
      }
    }
    .value {
      background: white;
      border: 1px solid #ddd;
      border-radius: 3px;
      padding: 8px;
      font-size: 13px;
      white-space: pre-wrap;
    }
  }
  
  .no-changes {
    color: #666;
    font-style: italic;
    text-align: center;
    padding: 10px;
    font-size: 13px;
  }
  
  .delete-warning {
    background: #f8d7da;
    color: #dc3545;
    padding: 12px;
    border-radius: 4px;
    font-size: 14px;
    i {
      margin-right: 8px;
    }
  }
  
  .close-btn {
    background: #cccccc;
    color: #333;
    border: 1px solid #bfbfbf;
    padding: 6px 15px;
    font-size: 14px;
    &:hover {
      background: #bfbfbf;
    }
  }
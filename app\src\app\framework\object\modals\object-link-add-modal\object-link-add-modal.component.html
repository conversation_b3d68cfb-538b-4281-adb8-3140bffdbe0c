<div class="modal-header bg-primary text-white">
  <h4 class="modal-title" *ngIf="primaryResult">Add connection to object:
    #{{primaryResult?.object_information?.object_id}} {{primaryResult?.type_information?.type_label}}</h4>
  <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
    <span aria-hidden="true">&times;</span>
  </button>
</div>
<form novalidate>
  <div class="modal-body">
    <div class="form-group" [formGroup]="form">
      <label for="secondary">Enter Public ID:</label>
      <input type="number" class="form-control" min="0" formControlName="secondary" name="secondary" id="secondary"
             [ngClass]="{ 'is-valid': secondary.valid && (secondary.dirty || secondary.touched),
                 'is-invalid': secondary.invalid && (secondary.dirty || secondary.touched)}"/>
      <small id="secondaryHelp" class="form-text text-muted float-left">Select the Public ID of another object</small>
      <div *ngIf="secondary.invalid && (secondary.dirty || secondary.touched)" class="invalid-feedback">
        <div class="float-right" *ngIf="secondary.errors.required">
          Please enter a valid Public ID
        </div>
        <div class="float-right" *ngIf="secondary.errors.sameID">
          You can`t link a object to itself.
        </div>
        <div class="float-right" *ngIf="secondary.errors.objectExists">
          Object not exists!
        </div>
        <div class="float-right" *ngIf="secondary.errors.objectProtected">
          No access to this object!
        </div>
        <div class="clearfix"></div>
      </div>
      <div class="clearfix"></div>
    </div>
  </div>
  <div class="modal-footer">
    <button type="button" class="btn btn-outline-dark" (click)="activeModal.dismiss('Close click')" >Close</button>
    <button type="button" class="btn btn-outline-primary" (click)="onSave()" [disabled]="!form.valid" >Save</button>
  </div>
</form>

<div class="dropdown" >
<button mat-button [matMenuTriggerFor]="externalLinksMenu" class="custom-button">
  <div class="dropdown-toggle total-title d-flex justify-object-view-items" aria-haspopup="true" aria-expanded="false">
    <div class="icon"><i class="fa fa-link"></i></div>
    <div class="name"><strong class="text-uppercase">External links</strong> <span>Total {{renderResult?.externals.length}}</span></div>
</div>
</button>
</div>

<mat-menu #externalLinksMenu="matMenu">
  <ng-container *ngIf="renderResult?.externals.length > 0; else noLinks">
    <div class="overflow-auto" style="max-height:20vh">
      <a mat-menu-item *ngFor="let ext of renderResult?.externals" [href]="getSantizeUrl(ext.href)" target="_blank">
        <mat-icon>{{ ext.icon }}</mat-icon>
        <span>{{ ext.label }}</span>
      </a>
    </div>
  </ng-container>
  <ng-template #noLinks>
    <button mat-menu-item disabled>No external links available.</button>
  </ng-template>
</mat-menu>



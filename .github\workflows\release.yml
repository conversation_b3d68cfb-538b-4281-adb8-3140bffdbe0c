name: Release
on: 
  release:
    types: [published]

jobs:
  build:
    runs-on: ubuntu-latest
    container: becongmbh/datagerry_ci
    steps:
    - name: checkout the source code
      uses: actions/checkout@v2

    # extract <tagname> from refs/tags/<tagname> and replace "/" characters 
    - name: extract Github tag name
      run: echo "TAG=$(TAG_EXTRACTED=${GITHUB_REF#refs/tags/}; echo ${TAG_EXTRACTED//\//-})" >> $GITHUB_ENV

    - name: setup version strings
      run: |
        echo "VERSION_STRING=$(echo ${TAG})" >> $GITHUB_ENV
        echo "VERSION_ID=$(echo ${TAG})" >> $GITHUB_ENV
        echo "VERSION_PATH=$(echo ${TAG})" >> $GITHUB_ENV
        echo "VERSION_DOCKER_TAG=$(echo ${TAG})" >> $GITHUB_ENV

    - name: build DATAGERRY
      run:  BUILDVAR_VERSION=${VERSION_STRING} BUILDVAR_VERSION_EXT=${VERSION_ID} BUILDVAR_DOCKER_TAG=${VERSION_DOCKER_TAG} make -e

    - name: copy artifacts to files.datagerry.com
      run: |
        sshpass -p "$SCP_PASS" ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SCP_USER@$SCP_HOST "mkdir -p $SCP_PATH/$VERSION_PATH/bin"
        sshpass -p "$SCP_PASS" scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null target/bin/datagerry $SCP_USER@$SCP_HOST:$SCP_PATH/$VERSION_PATH/bin
        sshpass -p "$SCP_PASS" ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SCP_USER@$SCP_HOST "mkdir -p $SCP_PATH/$VERSION_PATH/rpm"
        sshpass -p "$SCP_PASS" scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null target/rpm/RPMS/x86_64/DATAGERRY-*.rpm  $SCP_USER@$SCP_HOST:$SCP_PATH/$VERSION_PATH/rpm
        sshpass -p "$SCP_PASS" ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SCP_USER@$SCP_HOST "mkdir -p $SCP_PATH/$VERSION_PATH/targz"
        sshpass -p "$SCP_PASS" scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null target/targz/datagerry-*.tar.gz  $SCP_USER@$SCP_HOST:$SCP_PATH/$VERSION_PATH/targz
      env:
        SCP_HOST: ${{ secrets.SCP_HOST }}
        SCP_USER: ${{ secrets.SCP_USER }}
        SCP_PASS: ${{ secrets.SCP_PASS }}
        SCP_PATH: ${{ secrets.SCP_PATH_FILES }}

    - name: copy documentation to docs.datagerry.com
      run: |
        sshpass -p "$SCP_PASS" ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SCP_USER@$SCP_HOST "mkdir -p $SCP_PATH/$VERSION_PATH"
        sshpass -p "$SCP_PASS" ssh -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null $SCP_USER@$SCP_HOST "rm -Rf $SCP_PATH/$VERSION_PATH/*"
        sshpass -p "$SCP_PASS" scp -o StrictHostKeyChecking=no -o UserKnownHostsFile=/dev/null -r target/docs/*  $SCP_USER@$SCP_HOST:$SCP_PATH/$VERSION_PATH
      env:
        SCP_HOST: ${{ secrets.SCP_HOST }}
        SCP_USER: ${{ secrets.SCP_USER }}
        SCP_PASS: ${{ secrets.SCP_PASS }}
        SCP_PATH: ${{ secrets.SCP_PATH_DOCS }}

    - name: publish docker image to hub.docker.com
      run: |
        echo "$DOCKER_TOKEN" | docker login --username $DOCKER_USER --password-stdin
        docker push beconbmgh/datagerry:$VERSION_DOCKER_TAG
      env:
        DOCKER_USER: ${{ secrets.DOCKER_USER }}
        DOCKER_TOKEN: ${{ secrets.DOCKER_TOKEN }}

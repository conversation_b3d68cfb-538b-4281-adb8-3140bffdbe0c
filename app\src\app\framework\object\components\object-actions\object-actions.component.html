<div class="total-title d-flex justify-content-start">
  <div class="icon"><i class="fa fa-cogs"></i></div>
  <div class="name">
    <strong class="text-uppercase">{{renderResult?.type_information?.type_label}}</strong>
    <span class="d-flex justify-content-between align-items-start">
      <button routerLink="/framework/object/type/{{renderResult?.type_information?.type_id}}"
              class="btn btn-sm btn-link pl-0"
              title="object list">
            <i class="fas fa-list"></i>
      </button>
      <ng-container *ngIf="renderResult?.type_information?.active">
        <button *permissionLink="'base.framework.object.edit';requirements:'UPDATE';acl:acl"
                routerLink="/framework/object/edit/{{renderResult?.object_information?.object_id}}"
                class="btn btn-sm btn-link "
                title="edit">
        <i class="fas fa-edit"></i>
      </button>
      <button type="button" *permissionLink="['base.framework.object.view', 'base.framework.object.add'];requirements:['CREATE', 'READ'];acl:acl"
              routerLink="/framework/object/copy/{{renderResult?.object_information?.object_id}}"
              class="btn btn-sm btn-link"
              title="copy">
        <i class="fas fa-clone"></i>
      </button>
      <button type="button" class="btn btn-sm btn-link" *permissionLink="'base.framework.object.delete';requirements:'DELETE';acl:acl"
              (click)="handleDelete(renderResult?.object_information?.object_id)"
              title="delete">
        <i class="fas fa-trash-alt"></i>
      </button>
      <button type="button" *permissionLink="'base.framework.object.add';requirements:'CREATE';acl:acl"
              routerLink="/framework/object/add/{{renderResult?.type_information?.type_id}}"
              class="btn btn-sm btn-link pr-2"
              title="add new object">
        <i class="fas fa-file"></i>
      </button>
      </ng-container>
    </span>
  </div>
</div>

<app-loading-popup 
  [isVisible]="isLoading$ | async"
  message="Hang tight, we're getting things ready..."
></app-loading-popup>
<div id="object-header" class="card">
  <div class="card-header d-flex justify-content-between">
    <div>
      <div *permissionLink="'base.framework.object.edit'" class="custom-control custom-switch">
        <input type="checkbox" class="custom-control-input active-control" id="object-view-active"
               (change)="toggleChange()" [checked]="activeState">
        <label class="custom-control-label" for="object-view-active"></label>
      </div>
    </div>
    <div id="object-view-title">
      <cmdb-active-badge [activeStatus]="activeState"></cmdb-active-badge>
      {{ renderResult?.type_information?.type_label }} #{{ renderResult?.object_information?.object_id }}
    </div>
    <div id="object-view-version">Version: {{ renderResult?.object_information?.version }}</div>
  </div>
</div>

<cmdb-table [items]="links" [columns]="columns" [page]="page" [sort]="sort" [id]="'object-links-table'"
            [stateEnabled]="false" [emptyMessage]="'No links to or from this object'" [searchEnabled]="false"
            [totalItems]="totalLinks" [selectEnabled]="false" [loading]="loading" [pageSize]="limit"
            [customButtons]="[addButtonTemplate]"
            (pageChange)="onPageChange($event)" (pageSizeChange)="onPageSizeChange($event)"
            (sortChange)="onSortChange($event)">
</cmdb-table>

<ng-template #partnerTemplate let-item="item">
  <cmdb-object-links-table-partner-cell [objectID]="objectID" [link]="item"></cmdb-object-links-table-partner-cell>
</ng-template>

<ng-template #actionTemplate let-item="item">
  <cmdb-object-links-table-action-cell [objectID]="objectID" [link]="item" (deleteEmitter)="onShowDeleteModal($event)"
                                       [acl]="acl"></cmdb-object-links-table-action-cell>
</ng-template>

<!-- Custom table buttons -->
<ng-template #addButtonTemplate>
    <button type="button" (click)="onShowAddModal()" class="btn btn-success btn-sm"
            *permissionLink="'base.framework.object.add';acl:acl;requirements:'CREATE'"
    ><i
      class="fas fa-plus-circle"></i> Add
    </button>
</ng-template>

<div class="row">
    <div class="col-lg-3 col-md-12 mb-md-3">
        <div class="card">
            <div class="card-header">
                Unassigned types
            </div>
            <div class="card-body">
                <div class="alert alert-warning alert-dismissible fade show mb-0" role="alert" *ngIf="unAssignedTypes.length == 0">
                    <strong>No types!</strong> All existing types are assigned.
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div *ngIf="unAssignedTypes.length > 0" class="list-group">
                    <ng-container *ngFor="let type of unAssignedTypes">
                        <div
                            *permissionLink="'base.framework.type.view',acl:type?.acl;requirements:'READ'"
                            class="list-group-item list-group-item-action mt-2"
                        >
                            <div
                                [dndDraggable]="type"
                                [dndEffectAllowed]="effect"
                                (dndMoved)="onDragged(type, unAssignedTypes, 'move')"
                                class="d-flex w-100 justify-content-between"
                            >
                                {{type.label}} #{{type.public_id}}
                                <span class="dnd-handle" dndHandle><i class="fa fa-arrows-alt"></i></span>
                            </div>
                        </div>
                    </ng-container>
                </div>
            </div>
            <div class="card-footer">
                <small>A type can only be assigned to one category.</small>
            </div>
        </div>
    </div>
    <form [formGroup]="categoryForm" (ngSubmit)="onSubmit()" class="col-lg-9 col-md-12 needs-validation" novalidate>
        <div class="card">
            <div class="card-header">
                Basic information
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="category-name">Identifier<span class="required">*</span>:</label>
                            <input
                                [formControl]="name"
                                type="text"
                                class="form-control"
                                id="category-name"
                                placeholder="Enter a unique identifier"
                                [value]="name.value | nameGuide"
                                [ngClass]="{ 'is-valid': name.valid && (name.dirty || name.touched),
                                        'is-invalid': name.invalid && (name.dirty || name.touched)}"
                            >
                            <small class="form-text text-muted float-left">Unique identifier for the category</small>
                            <div *ngIf="name.invalid && (name.dirty || name.touched)" class="invalid-feedback">
                                <div class="float-right" *ngIf="name.errors.required">
                                    This field is required
                                </div>
                                <div class="float-right" *ngIf="name.errors.categoryExists">
                                    A category with this name already exists
                                </div>
                            </div>
                            <div class="clearfix"></div>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="category-label">Label:</label>
                            <input
                                formControlName="label"
                                type="text"
                                class="form-control"
                                id="category-label"
                                placeholder="Enter a display label"
                                [ngClass]="{ 'is-valid': label.valid && (label.dirty || label.touched),
                                            'is-invalid': label.invalid && (label.dirty || label.touched)}"
                            >
                            <small class="form-text text-muted">
                                If the field is left blank, a titled variant of the identifier is displayed.
                            </small>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="category-meta-icon">Icon:</label>
                            <div class="input-group" formGroupName="meta">
                                <div class="input-group-prepend">
                                    <span
                                        class="input-group-text"
                                        id="category-meta-icon-addon"
                                    >
                                        <i [ngClass]="icon.value"></i>
                                    </span>
                                </div>
                                <input
                                    [iconPicker]
                                    [ipPosition]="'bottom'"
                                    [ipFallbackIcon]="fallBackIcon"
                                    [ipIconPack]="['fa5']"
                                    [ipPlaceHolder]="'Choose an icon'"
                                    (iconPickerSelect)="onIconSelect($event)"
                                    formControlName="icon"
                                    class="form-control"
                                    id="category-meta-icon"
                                    placeholder="Choose an icon"
                                    [ngClass]="{ 'is-valid': icon.valid && (icon.dirty || icon.touched),
                                                'is-invalid': icon.invalid && (icon.dirty || icon.touched)}"
                                    >
                            </div>
                            <small class="form-text text-muted">
                                The icon is a meta value, which will only be visible in the frontend.
                            </small>
                        </div>
                    </div>
                    <div class="col-md-12">
                        <div class="form-group">
                            <label for="category-name">Parent:</label>
                            <ng-select
                                formControlName="parent"
                                placeholder="No parent category"
                                [items]="categories"
                                [virtualScroll]="true"
                                [loading]="categoriesLoading"
                                bindValue="public_id"
                                bindLabel="label"
                                class="form-control"
                                appendTo="body"
                                (scrollToEnd)="onScrollToEnd()"
                                [ngClass]="{ 'is-valid': parent.valid && (parent.dirty || parent.touched),
                                            'is-invalid': parent.invalid && (parent.dirty || parent.touched)}"
                            >
                                <ng-template ng-header-tmp>
                                    <small class="form-text text-muted" *ngIf="totalCategories">
                                        Loaded {{categories.length}} of {{totalCategories}}
                                    </small>
                                </ng-template>
                            </ng-select>
                        </div>
                    </div>
                </div>
                <hr/>
                <div class="row">
                    <div class="col-md-12 dnd">
                        <label for="category-types">Types:</label>
                        <section
                            id="category-types"
                            dndDropzone
                            class="drop-zone"
                            data-content="Drop types here"
                            (dndDrop)="onDrop($event, assignedTypes, true)"
                        >
                            <div class="drop-zone" dndPlaceholderRef data-content="Drop types here">
                            </div>
                            <ng-container *ngFor="let type of assignedTypes">
                                <li
                                    *permissionLink="'base.framework.type.view',acl:type?.acl;requirements:'READ'"
                                    [dndDraggable]="type"
                                    [dndEffectAllowed]="effect"
                                    (dndMoved)="onDragged(type, assignedTypes, 'move', true)"
                                    class="list-group-item"
                                >
                                    <div class="container">
                                        <div class="row">
                                            <div class="col-auto mr-auto">
                                                {{type?.label}} # {{type?.public_id}}
                                            </div>
                                            <div class="col-auto">
                                                <i class="fa fa-arrows-alt dnd-handle" dndHandle></i>
                                                <i class="fa fa-trash-alt ml-2 pointer" (click)="clickRemoveAssignedType(type)"></i>
                                            </div>
                                        </div>
                                    </div>
                                </li>
                            </ng-container>
                        </section>
                    </div>
                </div>
            </div>
            <div class="card-footer">
                <div class="btn-group float-right" role="group">
                    <button
                        class="btn btn-outline-danger mr-2"
                        type="button"
                        routerLink="/framework/category"
                    >
                        Cancel
                    </button>
                    <button
                        type="submit"
                        class="btn btn-success"
                        [disabled]="categoryForm.invalid"
                    >
                        Save
                    </button>
                </div>
                <div class="clearfix"></div>
            </div>
        </div>
    </form>
</div>


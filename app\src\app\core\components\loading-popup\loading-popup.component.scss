// src/app/shared/components/loading-popup/loading-popup.component.scss
.neo-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    backdrop-filter: blur(1.2px);
    display: flex;
    justify-content: center;
    align-items: center;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    z-index: 9999;
  
    &.visible {
      opacity: 1;
      visibility: visible;
    }
  }
  
  .neo-container {
    background: #ffffff;
    border-radius: 16px;
    padding: 2rem;
    width: 500px;
    border: 1px solid rgba(28, 28, 21, 0.1); // Dark border
    box-shadow: 0 8px 32px rgba(242, 133, 20, 0.1); // Orange accent shadow
    position: relative;
    overflow: hidden;
  }
  
  .connection-animation {
    position: absolute;
    top: -30px;
    left: 0;
    right: 0;
    height: 60px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 0 2rem;
  
    .server-node {
      width: 30px;
      height: 30px;
      background: #f28514; // Primary orange
      border-radius: 8px;
      animation: pulse-orange 1.5s infinite;
    }
  
    .data-line {
      flex: 1;
      height: 3px;
      background: linear-gradient(90deg, #f28514 50%, transparent 50%); // Orange
      background-size: 20px 100%;
      animation: data-flow 0.8s linear infinite;
      margin: 0 1rem;
    }
  
    .cloud-node {
      width: 40px;
      height: 40px;
      background: #1c1c15; // Dark color
      border-radius: 50%;
      position: relative;
      
      .pulse {
        position: absolute;
        width: 100%;
        height: 100%;
        border: 2px solid rgba(242, 133, 20, 0.3); // Orange accent
        border-radius: 50%;
        animation: cloud-pulse 2s infinite;
      }
    }
  }
  
  .holographic-spinner {
    width: 80px;
    height: 80px;
    margin: 2rem auto;
    position: relative;
    
    .core {
      position: absolute;
      width: 100%;
      height: 100%;
      background: radial-gradient(circle, #f28514 0%, #c46a10 100%); // Orange gradient
      border-radius: 50%;
      animation: core-glow 2s infinite;
    }
    
    .orbit {
      position: absolute;
      width: 120%;
      height: 120%;
      border: 2px solid #1c1c15; // Dark color
      border-radius: 50%;
      top: -10%;
      left: -10%;
      animation: orbit-spin 4s linear infinite;
      
      &::before {
        content: '';
        position: absolute;
        width: 12px;
        height: 12px;
        background: #1c1c15; // Dark color
        border-radius: 50%;
        top: -6px;
        left: calc(50% - 6px);
      }
    }
    
    .particles {
      position: absolute;
      width: 100%;
      height: 100%;
      animation: particle-rotate 6s linear infinite;
      
      &::after {
        content: '';
        position: absolute;
        width: 8px;
        height: 8px;
        background: #ff9b38; // Light orange
        border-radius: 50%;
        top: -4px;
        left: calc(50% - 4px);
      }
    }
  }
  
  .status-container {
    text-align: center;
    margin-top: 2rem;
  
    .status-message {
      color: #1c1c15; // Dark text
      font-size: 1.3rem;
      margin-bottom: 1.5rem;
      font-weight: 600;
      letter-spacing: 0.3px;
    }
  
    .progress-trail {
      height: 4px;
      background: rgba(28, 28, 21, 0.1); // Dark with opacity
      border-radius: 2px;
      overflow: hidden;
      
      .progress-fill {
        height: 100%;
        background: linear-gradient(90deg, #f28514, #ff9b38); // Orange gradient
        transition: width 0.3s ease;
      }
    }
  
    .stage-indicator {
      display: flex;
      justify-content: center;
      gap: 1.5rem;
      margin-top: 1.5rem;
      
      .stage {
        color: rgba(28, 28, 21, 0.4); // Muted dark
        font-size: 0.95rem;
        position: relative;
        font-weight: 500;
        
        &.active {
          color: #f28514; // Orange accent
          font-weight: 600;
          
          &::after {
            content: '';
            position: absolute;
            bottom: -4px;
            left: 0;
            right: 0;
            height: 2px;
            background: linear-gradient(90deg, #f28514, #ff9b38);
            animation: stage-progress 2s infinite;
          }
        }
      }
    }
  }
  
  // Animations
  @keyframes data-flow {
    0% { background-position: 0 0; }
    100% { background-position: 20px 0; }
  }
  
  @keyframes cloud-pulse {
    0% { transform: scale(1); opacity: 1; }
    100% { transform: scale(2); opacity: 0; }
  }
  
  @keyframes core-glow {
    0%, 100% { filter: brightness(1); }
    50% { filter: brightness(1.15); }
  }
  
  @keyframes orbit-spin {
    from { transform: rotate(0deg); }
    to { transform: rotate(360deg); }
  }
  
  @keyframes particle-rotate {
    from { transform: rotate(0deg); }
    to { transform: rotate(-360deg); }
  }
  
  @keyframes stage-progress {
    0% { opacity: 0; width: 0; }
    50% { opacity: 1; width: 100%; }
    100% { opacity: 0; width: 0; }
  }
  
  @keyframes pulse-orange {
    0% { box-shadow: 0 0 0 0 rgba(242, 133, 20, 0.4); }
    70% { box-shadow: 0 0 0 12px rgba(242, 133, 20, 0); }
    100% { box-shadow: 0 0 0 0 rgba(242, 133, 20, 0); }
  }
<div class="container-fluid p-0 m-0">
    <div class="row">
        <div class="col-sm-10">
            <h1>
                Add new object: <strong>{{typeInstance?.label}}</strong>
            </h1>
        </div>
        <div class="col-sm-2 text-right" *ngIf="typeInstance">
            <div class="btn-group p-1" role="group">
                <button
                type="button"
                class="btn btn-outline-danger mr-2"
                routerLink="/framework/object/type/{{typeInstance?.public_id}}"
                >Cancel</button>

                <button
                    type="submit"
                    class="btn btn-success mr-2"
                    (click)="saveObject()"
                    [disabled]="!renderForm.valid && !isSaving"
                >Save</button>
            </div>
        </div>
    </div>
</div>

<hr/>

<ng-template [ngIf]="!typeInstance" [ngIfElse]="renderTemplate">
    <form [formGroup]="typeIDForm" novalidate>
        <div class="form-group row">
            <label class="col-sm-2 col-form-label">
                Select type:
            </label>
            <div class="col-sm-10">
                <div class="input-group mb-3">
                    <ng-select
                        [items]="typeList"
                        formControlName="typeID"
                        bindLabel="label"
                        bindValue="public_id"
                        required
                    >
                        <ng-template ng-optgroup-tmp let-item="item">
                            <span style="vertical-align: middle;">{{item.name}}</span>
                            <span class="ml-1 badge badge-secondary">{{item.total}}</span>
                        </ng-template>
                    </ng-select>
                    <div class="input-group-append">
                        <button class="btn btn-success" type="submit" [disabled]="!typeIDForm.valid" (click)="useTypeID()">
                            <i class="fas fa-plus"></i> Add
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </form>
</ng-template>

<ng-template #renderTemplate>
    <form
        [formGroup]="renderForm"
        class="renderForm needs-validation"
        novalidate
        autocomplete="off"
        (ngSubmit)="saveObject()"
    >
        <cmdb-render
            [mode]="mode"
            [renderForm]="renderForm"
            [typeInstance]="typeInstance"
        />

        <div id="object-form-action" class="shadow p-3 mb-5 bg-white rounded text-center">
            <div class="row d-flex justify-content-center align-items-center">
                <p class="pt-3 mx-4">
                    Do you want to save the new object of <strong>{{typeInstance?.label}}</strong> you made before quitting?
                </p>
                <div class="btn-group" role="group">
                    <button
                        type="button"
                        class="btn btn-outline-danger mr-2"
                        routerLink="/framework/object/type/{{typeInstance?.public_id}}"
                    >Cancel</button>

                    <button
                        type="submit"
                        class="btn btn-success mr-2"
                        [disabled]="!renderForm.valid"
                    >Save</button>
                </div>
            </div>
        </div>
    </form>
</ng-template>

<app-loading-popup 
  [isVisible]="isLoading$ | async"
  message="We're Processing the Data..."
></app-loading-popup>
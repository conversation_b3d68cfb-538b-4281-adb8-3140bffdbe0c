<i [ngClass]="node.category.meta.icon ? node.category.meta.icon : 'far fa-folder-open'"></i>
<ng-content></ng-content>
<ng-container *ngIf="mode == 2">
  <span class="dnd-handle ml-2" dndHandle><i class="fa fa-arrows-alt"></i></span>
  <span class="float-right">
  <a class="ml-2" [routerLink]="['/','framework','category','edit', node.category.public_id]">
    <i class="fas fa-edit"></i>
  </a>
  <a class="pointer text-primary" *permissionLink="'base.framework.category.delete'" (click)="onDelete(node.category)">
    <i class="fas fa-trash"></i>
  </a>
</span>
</ng-container>

 <div class="modal-content">
    <div class="modal-header">
      <h3 class="modal-title">Add new Category</h3>
      <button type="button" class="close" aria-label="Close" (click)="activeModal.dismiss('Cross click')">
        <span aria-hidden="true">&times;</span>
      </button>
    </div>
    <div class="modal-body">
      <form [formGroup]="catAddForm">
        <div class="form-group">
          <label for="category-name">Name<span class="required">*</span>:</label>
          <input [formControl]="name" type="text" class="form-control" id="category-name"
                 placeholder="Enter a unique name" [value]="name.value | nameGuide"
                 [ngClass]="{ 'is-valid': name.valid && (name.dirty || name.touched), 'is-invalid': name.invalid && (name.dirty || name.touched)}">
          <small class="form-text text-muted float-left">Unique name with which the category can be
            identified</small>
          <div *ngIf="name.invalid && (name.dirty || name.touched)" class="invalid-feedback">
            <div class="float-right" *ngIf="name.errors.required">
              This field is required
            </div>
            <div class="float-right" *ngIf="name.errors.categoryExists">
              A category with this name already exists
            </div>
          </div>
          <div class="clearfix"></div>
        </div>
        <div class="form-group">
          <label for="category-label">Label:</label>
          <input formControlName="label" type="text" class="form-control" id="category-label"
                 placeholder="Enter a display label"
                 [ngClass]="{ 'is-valid': label.valid && (label.dirty || label.touched), 'is-invalid': label.invalid && (label.dirty || label.touched)}">
          <small class="form-text text-muted">If the field is left blank, a titled variant of the name is
            displayed.</small>
        </div>
      </form>
    </div>
    <div class="modal-footer">
      <button type="button" class="btn btn-primary" data-dismiss="modal" [disabled]="!catAddForm.valid"
              (click)="activeModal.close(catAddForm)">Save
      </button>
      <button type="button" class="btn btn-secondary" (click)="activeModal.close(null)">Cancel</button>
    </div>
  </div>

<table id="summaries-table" class="table table-sm table-hover">
    <tbody>
        <tr *ngFor="let sum of summaries">
            <th scope="row">{{ sum.label }}</th>
            <td>
                <cmdb-render-element
                    [mode]="mode"
                    [data]="sum"
                ></cmdb-render-element>
            </td>
            <td class="clearfix">
                <button
                    type="button"
                    *ngIf="sum.value"
                    class="btn btn-outline-secondary btn-sm float-right"
                    (click)="
                        sum.value.$date
                            ? clipBoardFormattedSummary(sum.value)
                            : clipBoardSummary( sum, sum.value)
                    "
                >
                    <fa-icon [icon]="['far', 'clipboard']"></fa-icon>
                </button>
            </td>
        </tr>
    </tbody>
</table>

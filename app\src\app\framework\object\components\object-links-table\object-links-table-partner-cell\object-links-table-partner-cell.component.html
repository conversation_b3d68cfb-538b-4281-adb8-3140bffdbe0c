<ng-container *ngIf="partnerObject; else error;">
  <i class="{{partnerObject.type_information.icon}}"></i>
  <a [routerLink]="['/', 'framework', 'object', 'view', partnerObject.object_information.object_id]">
    #{{partnerObject.object_information.object_id}} {{partnerObject.type_information.type_label}}
  </a>
  <span *ngIf="partnerObject.summaries.length > 0">{{partnerObject.summary_line}}</span>
</ng-container>

<ng-template #error>
  <ng-container *ngIf="errorLoading; else loading;">
    <div class="text-center text-danger">
      Partner object could not be found.
    </div>
  </ng-container>
</ng-template>

<ng-template #loading>
  <div class="text-center">
    <div class="spinner-border spinner-border-sm" role="status">
      <span class="sr-only">Loading...</span>
    </div>
  </div>
</ng-template>

<h1>Categories Overview</h1>
<div class="float-right">
  <ng-container *permissionLink="['base.framework.category.add']">
    <button [routerLink]="['/framework/category/add']" type="button" class="btn btn-sm btn-success">
      <i class="fas fa-plus"></i> Add
    </button>
  </ng-container>
  <ng-container *permissionLink="['base.framework.category.edit']">
    <button *ngIf="mode != 2; else listButton" [routerLink]="['/framework/category/edit']" type="button"
            class="btn btn-sm btn-secondary ml-2 text-white">
      <i class="fas fa-edit"></i> Edit
    </button>
  </ng-container>
  <ng-template #listButton>
    <button [routerLink]="['/framework/category/']" type="button" class="btn btn-sm btn-secondary ml-2 text-white">
      <i class="fas fa-list"></i> List
    </button>
  </ng-template>
</div>
<div class="clearfix"></div>
<hr/>
<div class="row">
  <div id="category-tree" class="col-sm-12 col-md-4 border-right">
    <div class="card">
      <div class="card-header">
        Category-Tree
      </div>
      <div class="card-body">
        <div class="tree-view">
          <cmdb-category-tree [mode]="mode" [tree]="categoryTree" (change)="onTreeChange()"></cmdb-category-tree>
        </div>
      </div>
      <div *ngIf="mode == 2;" class="card-footer">
        <div  class="btn-group float-right" role="group">
          <button type="submit" class="btn btn-success" (click)="onSave()">Save</button>
        </div>
      </div>
    </div>
  </div>
  <div id="category-list" class="col-sm-12 col-md-8">
    <div class="card">
      <div class="card-header">Category-List</div>
      <div class="card-body">
        <cmdb-table [id]="id" [items]="categories" [columns]="tableColumns" [stateEnabled]="true"
                    [sort]="sort" [totalItems]="totalResults" [pageSize]="apiParameters.limit"
                    [page]="apiParameters.page" [emptyMessage]="'There are no categories for this list yet!'"
                    [sortable]="true" [searchEnabled]="false" [pageSizeEnabled]="true"
                    (sortChange)="onSortChange($event)" (stateReset)="onStateReset()" (stateSelect)="onStateSelect($event)"
                    (pageSizeChange)="onPageSizeChange($event)" (pageChange)="onPageChange($event)">
        </cmdb-table>
      </div>
    </div>
  </div>
</div>


<app-loading-popup 
  [isVisible]="isLoading$ | async"
  message="We're Loading the Categories..."
></app-loading-popup>